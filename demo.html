<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Hunt - 功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .demo-btn {
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 18px;
        }

        .demo-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .feature-desc {
            opacity: 0.9;
            line-height: 1.6;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
        }

        .tech-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tech-item h4 {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .tech-item p {
            opacity: 0.8;
            font-size: 14px;
        }

        .screenshots {
            margin-bottom: 40px;
        }

        .screenshots-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }

        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .screenshot {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .screenshot-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer p {
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: white;
            text-decoration: none;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .footer-link:hover {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 18px;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">Color Hunt</h1>
            <p class="subtitle">一个现代化的颜色调色板网站，使用 PHP 开发，具有响应式设计和优化的性能</p>
            <a href="index.php" class="demo-btn">🎨 查看演示</a>
        </header>

        <section class="features">
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">美观的调色板</h3>
                <p class="feature-desc">精心设计的调色板展示，支持多种分类和风格，每个调色板都经过精心挑选。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">响应式设计</h3>
                <p class="feature-desc">完美适配手机、平板和桌面设备，在任何屏幕尺寸下都有最佳的用户体验。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">性能优化</h3>
                <p class="feature-desc">启用压缩、缓存和懒加载，确保网站快速加载，提供流畅的浏览体验。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3 class="feature-title">智能搜索</h3>
                <p class="feature-desc">支持按颜色代码、分类搜索，带有防抖功能，提供精准的搜索结果。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📋</div>
                <h3 class="feature-title">一键复制</h3>
                <p class="feature-desc">点击即可复制单个颜色或整个调色板，支持多种浏览器和设备。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">❤️</div>
                <h3 class="feature-title">互动功能</h3>
                <p class="feature-desc">点赞系统、分页浏览、键盘快捷键，丰富的交互功能提升用户体验。</p>
            </div>
        </section>

        <section class="tech-stack">
            <h2 class="tech-title">技术栈</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>PHP 7.4+</h4>
                    <p>后端开发语言，无需复杂框架</p>
                </div>
                <div class="tech-item">
                    <h4>原生 CSS</h4>
                    <p>不依赖第三方 CSS 框架</p>
                </div>
                <div class="tech-item">
                    <h4>现代 JavaScript</h4>
                    <p>ES6+ 语法和现代 API</p>
                </div>
                <div class="tech-item">
                    <h4>RESTful API</h4>
                    <p>标准的 API 接口设计</p>
                </div>
                <div class="tech-item">
                    <h4>JSON 存储</h4>
                    <p>简单的文件数据存储</p>
                </div>
                <div class="tech-item">
                    <h4>Apache 优化</h4>
                    <p>压缩、缓存和安全配置</p>
                </div>
            </div>
        </section>

        <section class="screenshots">
            <h2 class="screenshots-title">功能展示</h2>
            <div class="screenshot-grid">
                <div class="screenshot">
                    <div class="screenshot-placeholder">🏠</div>
                    <h4>主页面</h4>
                    <p>调色板网格展示</p>
                </div>
                <div class="screenshot">
                    <div class="screenshot-placeholder">🔍</div>
                    <h4>搜索功能</h4>
                    <p>智能搜索和筛选</p>
                </div>
                <div class="screenshot">
                    <div class="screenshot-placeholder">📱</div>
                    <h4>移动端</h4>
                    <p>响应式移动体验</p>
                </div>
                <div class="screenshot">
                    <div class="screenshot-placeholder">⚙️</div>
                    <h4>API 接口</h4>
                    <p>RESTful API 设计</p>
                </div>
            </div>
        </section>

        <footer class="footer">
            <p>Color Hunt - 发现美丽的颜色调色板</p>
            <div class="footer-links">
                <a href="index.php" class="footer-link">主页</a>
                <a href="api.php?action=palettes" class="footer-link">API</a>
                <a href="test.php" class="footer-link">测试</a>
                <a href="README.md" class="footer-link">文档</a>
            </div>
        </footer>
    </div>
</body>
</html>
