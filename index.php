<?php
// 颜色调色板数据
$palettes = [
    [
        'id' => 1,
        'colors' => ['#1e3a8a', '#3b82f6', '#93c5fd', '#dbeafe'],
        'likes' => 11,
        'time' => '2 hours'
    ],
    [
        'id' => 2,
        'colors' => ['#f3e8ff', '#c4b5fd', '#8b5cf6', '#7c3aed'],
        'likes' => 122,
        'time' => 'Yesterday'
    ],
    [
        'id' => 3,
        'colors' => ['#be185d', '#ec4899', '#f9a8d4', '#fce7f3'],
        'likes' => 168,
        'time' => '2 days'
    ],
    [
        'id' => 4,
        'colors' => ['#a7f3d0', '#6ee7b7', '#34d399', '#10b981'],
        'likes' => 296,
        'time' => '3 days'
    ],
    [
        'id' => 5,
        'colors' => ['#065f46', '#047857', '#059669', '#10b981'],
        'likes' => 206,
        'time' => '4 days'
    ],
    [
        'id' => 6,
        'colors' => ['#0891b2', '#06b6d4', '#67e8f9', '#cffafe'],
        'likes' => 294,
        'time' => '5 days'
    ],
    [
        'id' => 7,
        'colors' => ['#374151', '#6b7280', '#9ca3af', '#d1d5db'],
        'likes' => 334,
        'time' => '6 days'
    ],
    [
        'id' => 8,
        'colors' => ['#7c2d12', '#dc2626', '#ef4444', '#fca5a5'],
        'likes' => 257,
        'time' => '1 week'
    ],
    [
        'id' => 9,
        'colors' => ['#d1d5db', '#f97316', '#1f2937', '#000000'],
        'likes' => 341,
        'time' => '1 week'
    ],
    [
        'id' => 10,
        'colors' => ['#84cc16', '#a3e635', '#dc2626', '#f97316'],
        'likes' => 297,
        'time' => '1 week'
    ],
    [
        'id' => 11,
        'colors' => ['#f3e8ff', '#ea580c', '#1f2937', '#7c2d12'],
        'likes' => 458,
        'time' => '1 week'
    ],
    [
        'id' => 12,
        'colors' => ['#3b82f6', '#fbbf24', '#f59e0b', '#d97706'],
        'likes' => 555,
        'time' => '1 week'
    ]
];

$categories = [
    'New', 'Popular', 'Random', 'Collection', 'Pastel', 'Vintage', 
    'Retro', 'Neon', 'Gold', 'Light', 'Dark', 'Warm', 'Cold', 
    'Summer', 'Fall', 'Winter', 'Spring'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Hunt - 颜色调色板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
            text-decoration: none;
        }

        .logo::before {
            content: "🎨";
            margin-right: 10px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            margin: 0 30px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #3b82f6;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
        }

        /* Sidebar */
        .main-content {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        .sidebar {
            width: 200px;
            flex-shrink: 0;
        }

        .sidebar-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sidebar-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .category-list {
            list-style: none;
        }

        .category-item {
            padding: 8px 0;
            cursor: pointer;
            color: #64748b;
            transition: color 0.3s;
        }

        .category-item:hover,
        .category-item.active {
            color: #3b82f6;
        }

        .category-item.active {
            font-weight: 600;
        }

        /* Content Area */
        .content {
            flex: 1;
        }

        .palettes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
        }

        .palette-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .palette-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .color-strip {
            display: flex;
            height: 200px;
        }

        .color-block {
            flex: 1;
            position: relative;
            transition: transform 0.3s;
        }

        .color-block:hover {
            transform: scale(1.05);
            z-index: 10;
        }

        .color-code {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .color-block:hover .color-code {
            opacity: 1;
        }

        .palette-info {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .like-button {
            display: flex;
            align-items: center;
            gap: 5px;
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            font-size: 14px;
            transition: color 0.3s;
        }

        .like-button:hover {
            color: #ef4444;
        }

        .time-info {
            color: #94a3b8;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .search-box {
                margin: 0;
                max-width: none;
            }

            .main-content {
                flex-direction: column;
                gap: 20px;
                margin-top: 20px;
            }

            .sidebar {
                width: 100%;
            }

            .sidebar-section {
                padding: 15px;
            }

            .category-list {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
            }

            .category-item {
                background: #f1f5f9;
                padding: 8px 12px;
                border-radius: 20px;
                font-size: 14px;
            }

            .palettes-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 20px;
            }

            .color-strip {
                height: 150px;
            }

            .container {
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .palettes-grid {
                grid-template-columns: 1fr;
            }

            .logo {
                font-size: 20px;
            }

            .search-input {
                font-size: 14px;
                padding: 10px 15px 10px 40px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">Color Hunt</a>
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="搜索调色板...">
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content">
            <aside class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">分类</div>
                    <ul class="category-list">
                        <?php foreach ($categories as $index => $category): ?>
                            <li class="category-item <?= $index === 0 ? 'active' : '' ?>">
                                <?= $category ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </aside>

            <main class="content">
                <div class="palettes-grid">
                    <?php foreach ($palettes as $palette): ?>
                        <div class="palette-card" data-palette-id="<?= $palette['id'] ?>">
                            <div class="color-strip">
                                <?php foreach ($palette['colors'] as $color): ?>
                                    <div class="color-block" style="background-color: <?= $color ?>">
                                        <div class="color-code"><?= strtoupper($color) ?></div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="palette-info">
                                <button class="like-button" onclick="toggleLike(<?= $palette['id'] ?>)">
                                    <span class="heart">♡</span>
                                    <span class="like-count"><?= $palette['likes'] ?></span>
                                </button>
                                <span class="time-info"><?= $palette['time'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 点赞功能
        function toggleLike(paletteId) {
            const button = event.target.closest('.like-button');
            const heart = button.querySelector('.heart');
            const countSpan = button.querySelector('.like-count');
            let count = parseInt(countSpan.textContent);
            
            if (heart.textContent === '♡') {
                heart.textContent = '♥';
                heart.style.color = '#ef4444';
                count++;
            } else {
                heart.textContent = '♡';
                heart.style.color = '';
                count--;
            }
            
            countSpan.textContent = count;
        }

        // 分类切换
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelector('.category-item.active').classList.remove('active');
                this.classList.add('active');
            });
        });

        // 颜色复制功能
        document.querySelectorAll('.color-block').forEach(block => {
            block.addEventListener('click', function() {
                const color = this.style.backgroundColor;
                const colorCode = this.querySelector('.color-code').textContent;
                
                navigator.clipboard.writeText(colorCode).then(() => {
                    // 显示复制成功提示
                    const toast = document.createElement('div');
                    toast.textContent = `已复制 ${colorCode}`;
                    toast.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #10b981;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 8px;
                        z-index: 1000;
                        animation: slideIn 0.3s ease;
                    `;
                    
                    document.body.appendChild(toast);
                    setTimeout(() => {
                        toast.remove();
                    }, 2000);
                });
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.palette-card');
            
            cards.forEach(card => {
                const colors = Array.from(card.querySelectorAll('.color-code'))
                    .map(el => el.textContent.toLowerCase());
                
                const matches = colors.some(color => color.includes(searchTerm));
                card.style.display = matches || searchTerm === '' ? 'block' : 'none';
            });
        });

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
