<?php
// 性能优化：启用输出缓冲和压缩
ob_start();
if (extension_loaded('zlib')) {
    ob_start('ob_gzhandler');
}

// 设置缓存头
header('Cache-Control: public, max-age=3600');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

// 直接从数据文件获取数据
function fetchPalettes($search = '', $category = '', $page = 1, $limit = 12) {
    $dataFile = 'data/palettes.json';

    // 确保数据文件存在
    if (!file_exists($dataFile)) {
        return ['palettes' => [], 'total' => 0, 'page' => 1, 'pages' => 0];
    }

    $data = json_decode(file_get_contents($dataFile), true);
    if (!$data || !isset($data['palettes'])) {
        return ['palettes' => [], 'total' => 0, 'page' => 1, 'pages' => 0];
    }

    $palettes = $data['palettes'];

    // 筛选
    if ($search || $category) {
        $palettes = array_filter($palettes, function($palette) use ($search, $category) {
            $matchesSearch = empty($search) ||
                stripos(implode('', $palette['colors']), $search) !== false ||
                stripos($palette['category'], $search) !== false;
            $matchesCategory = empty($category) ||
                strtolower($palette['category']) === strtolower($category);
            return $matchesSearch && $matchesCategory;
        });
    }

    // 排序（按最新）
    usort($palettes, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    // 分页
    $total = count($palettes);
    $offset = ($page - 1) * $limit;
    $palettes = array_slice($palettes, $offset, $limit);

    return [
        'palettes' => array_values($palettes),
        'total' => $total,
        'page' => $page,
        'pages' => ceil($total / $limit)
    ];
}

// 获取分类列表
function fetchCategories() {
    $dataFile = 'data/palettes.json';

    if (!file_exists($dataFile)) {
        return [];
    }

    $data = json_decode(file_get_contents($dataFile), true);
    if (!$data || !isset($data['palettes'])) {
        return [];
    }

    $categories = array_unique(array_column($data['palettes'], 'category'));
    sort($categories);
    return $categories;
}

// 处理搜索和筛选
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$page = (int)($_GET['page'] ?? 1);

// 获取数据
$paletteData = fetchPalettes($search, $category, $page);
$palettes = $paletteData['palettes'] ?? [];
$totalPages = $paletteData['pages'] ?? 1;

// 获取实际的分类列表
$actualCategories = fetchCategories();
$categories = array_merge(['All'], array_map('ucfirst', $actualCategories));
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Hunt - 颜色调色板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
            text-decoration: none;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #3b82f6;
        }

        /* Sidebar */
        .main-content {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        .sidebar {
            width: 200px;
            flex-shrink: 0;
        }

        .category-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .category-item {
            display: block;
            padding: 10px 15px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }

        .category-item:hover,
        .category-item.active {
            background: #f1f5f9;
            color: #1e293b;
        }

        /* Palette Grid */
        .palette-grid {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
        }

        .palette-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .palette-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .color-strip {
            height: 200px;
            display: flex;
        }

        .color-block {
            flex: 1;
            position: relative;
            transition: transform 0.3s;
        }

        .color-block:hover {
            transform: scale(1.05);
            z-index: 10;
        }

        .color-code {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .color-block:hover .color-code {
            opacity: 1;
        }

        .palette-info {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .like-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 20px;
            transition: all 0.3s;
        }

        .like-btn:hover {
            background: #f1f5f9;
            color: #e11d48;
        }

        .time-info {
            color: #94a3b8;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .main-content {
                flex-direction: column;
                gap: 20px;
            }

            .sidebar {
                width: 100%;
            }

            .category-list {
                display: flex;
                overflow-x: auto;
                padding: 15px;
                gap: 10px;
            }

            .category-item {
                white-space: nowrap;
                margin-bottom: 0;
            }

            .palette-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 20px;
            }

            .color-strip {
                height: 150px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            .palette-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .color-strip {
                height: 120px;
            }
        }

        /* No results */
        .no-results {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .no-results-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .no-results h3 {
            margin-bottom: 10px;
            color: #1e293b;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 40px;
            padding: 20px;
        }

        .page-btn {
            padding: 10px 15px;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.3s;
            font-weight: 500;
        }

        .page-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }

        .page-btn.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        /* Loading animation */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #64748b;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1e293b;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Color picker modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
            padding: 5px;
        }

        .close-btn:hover {
            color: #1e293b;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="logo">
                    <div class="logo-icon">🎨</div>
                    Color Hunt
                </a>
                <div class="search-box">
                    <form method="GET" action="">
                        <input type="text" 
                               class="search-input" 
                               name="search" 
                               placeholder="搜索调色板..." 
                               value="<?= htmlspecialchars($search) ?>">
                        <?php if ($category): ?>
                            <input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>">
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content">
            <aside class="sidebar">
                <nav class="category-list">
                    <?php foreach ($categories as $cat): ?>
                        <?php
                        $catValue = $cat === 'All' ? '' : strtolower($cat);
                        $isActive = ($cat === 'All' && empty($category)) || (strtolower($category) === strtolower($cat));
                        ?>
                        <a href="?<?= $catValue ? 'category=' . urlencode($catValue) : '' ?>"
                           class="category-item <?= $isActive ? 'active' : '' ?>">
                            <?= $cat ?>
                        </a>
                    <?php endforeach; ?>
                </nav>
            </aside>

            <main class="palette-grid" id="paletteGrid">
                <?php if (empty($palettes)): ?>
                    <div class="no-results">
                        <div class="no-results-icon">🎨</div>
                        <h3>没有找到调色板</h3>
                        <p>尝试调整搜索条件或浏览其他分类</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($palettes as $palette): ?>
                        <div class="palette-card" data-id="<?= $palette['id'] ?>" onclick="copyPalette(<?= $palette['id'] ?>)">
                            <div class="color-strip">
                                <?php foreach ($palette['colors'] as $index => $color): ?>
                                    <div class="color-block"
                                         style="background-color: <?= $color ?>"
                                         data-color="<?= $color ?>"
                                         onclick="copyColor('<?= $color ?>', event)">
                                        <span class="color-code"><?= strtoupper($color) ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="palette-info">
                                <button class="like-btn" onclick="likePalette(<?= $palette['id'] ?>, event)">
                                    ❤️ <span class="like-count"><?= $palette['likes'] ?></span>
                                </button>
                                <span class="time-info"><?= $palette['time'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </main>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="page-btn">
                            ← 上一页
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                           class="page-btn <?= $i === $page ? 'active' : '' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="page-btn">
                            下一页 →
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>加载中...</p>
    </div>

    <script>
        // 全局变量
        let isLoading = false;
        let currentPage = <?= $page ?>;
        let totalPages = <?= $totalPages ?>;

        // 复制颜色代码
        function copyColor(color, event) {
            event.stopPropagation();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(color).then(() => {
                    showToast(`已复制颜色: ${color}`);
                }).catch(() => {
                    fallbackCopyTextToClipboard(color);
                });
            } else {
                fallbackCopyTextToClipboard(color);
            }
        }

        // 兼容性复制函数
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast(`已复制: ${text}`);
            } catch (err) {
                showToast('复制失败，请手动复制');
            }

            document.body.removeChild(textArea);
        }

        // 复制整个调色板
        function copyPalette(paletteId) {
            const palette = document.querySelector(`[data-id="${paletteId}"]`);
            const colors = Array.from(palette.querySelectorAll('.color-block')).map(block =>
                block.getAttribute('data-color')
            );
            const colorCodes = colors.join(', ');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(colorCodes).then(() => {
                    showToast('已复制调色板!');
                }).catch(() => {
                    fallbackCopyTextToClipboard(colorCodes);
                });
            } else {
                fallbackCopyTextToClipboard(colorCodes);
            }
        }

        // 点赞功能
        async function likePalette(paletteId, event) {
            event.stopPropagation();

            if (isLoading) return;
            isLoading = true;

            const btn = event.target.closest('.like-btn');
            const likeCount = btn.querySelector('.like-count');
            const currentLikes = parseInt(likeCount.textContent);

            try {
                const response = await fetch('api.php?action=like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: paletteId })
                });

                const data = await response.json();

                if (data.success) {
                    likeCount.textContent = data.likes;
                    btn.style.color = '#e11d48';
                    showToast('已点赞!');
                } else {
                    throw new Error(data.error || '点赞失败');
                }
            } catch (error) {
                console.error('点赞失败:', error);
                showToast('点赞失败，请稍后重试');
            } finally {
                isLoading = false;
            }
        }

        // 显示提示
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;

            if (type === 'error') {
                toast.style.background = '#dc2626';
            } else if (type === 'success') {
                toast.style.background = '#059669';
            }

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // 搜索防抖
        let searchTimeout;
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.form.submit();
                }, 500);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K 聚焦搜索框
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                searchInput?.focus();
            }

            // ESC 清空搜索
            if (e.key === 'Escape' && document.activeElement === searchInput) {
                searchInput.value = '';
                searchInput.form.submit();
            }
        });

        // 懒加载图片优化
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                        observer.unobserve(card);
                    }
                });
            });

            document.querySelectorAll('.palette-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.5s, transform 0.5s';
                observer.observe(card);
            });
        }

        // 添加滑出动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载状态
            const links = document.querySelectorAll('a[href*="page="], .category-item');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    document.getElementById('loading').style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
