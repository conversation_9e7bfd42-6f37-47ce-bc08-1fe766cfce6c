<?php
require_once '../config/database.php';

// 获取统计数据
try {
    $db = getDB();
    
    $totalPalettes = $db->fetchOne("SELECT COUNT(*) as count FROM palettes WHERE is_active = 1")['count'];
    $totalCategories = $db->fetchOne("SELECT COUNT(*) as count FROM categories WHERE is_active = 1")['count'];
    $totalLikes = $db->fetchOne("SELECT SUM(likes_count) as total FROM palettes")['total'] ?? 0;
    $totalViews = $db->fetchOne("SELECT SUM(views_count) as total FROM palettes")['total'] ?? 0;
    
    // 最受欢迎的调色板
    $popularPalettes = $db->fetchAll("
        SELECT p.*, c.name as category_name 
        FROM palettes p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 
        ORDER BY p.likes_count DESC 
        LIMIT 5
    ");
    
    // 最新的调色板
    $recentPalettes = $db->fetchAll("
        SELECT p.*, c.name as category_name 
        FROM palettes p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Hunt - 后台管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .admin-header {
            background: #1e293b;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            font-size: 24px;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: background 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #3b82f6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .palette-list {
            display: grid;
            gap: 15px;
        }

        .palette-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s;
        }

        .palette-item:hover {
            border-color: #3b82f6;
        }

        .palette-colors {
            display: flex;
            gap: 2px;
        }

        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .palette-info {
            flex: 1;
        }

        .palette-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .palette-meta {
            font-size: 14px;
            color: #64748b;
        }

        .palette-stats {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: #64748b;
        }

        .btn {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .error {
            background: #fef2f2;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .admin-nav {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                gap: 15px;
            }

            .dashboard-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .palette-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .palette-stats {
                justify-content: space-between;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-logo">🎨 Color Hunt 管理后台</div>
            <div class="nav-links">
                <a href="index.php" class="active">仪表板</a>
                <a href="palettes.php">调色板管理</a>
                <a href="categories.php">分类管理</a>
                <a href="../index.php" target="_blank">查看前台</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <?php if (isset($error)): ?>
            <div class="error">
                ❌ 数据库连接错误: <?= htmlspecialchars($error) ?><br>
                <a href="../install/install.php">点击这里安装数据库</a>
            </div>
        <?php else: ?>
            
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= $totalPalettes ?></div>
                    <div class="stat-label">调色板总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $totalCategories ?></div>
                    <div class="stat-label">分类总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $totalLikes ?></div>
                    <div class="stat-label">总点赞数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $totalViews ?></div>
                    <div class="stat-label">总浏览数</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">
                    最受欢迎的调色板
                    <a href="palettes.php" class="btn" style="float: right;">管理所有调色板</a>
                </div>
                <div class="palette-list">
                    <?php foreach ($popularPalettes as $palette): ?>
                        <?php $colors = json_decode($palette['colors'], true); ?>
                        <div class="palette-item">
                            <div class="palette-colors">
                                <?php foreach ($colors as $color): ?>
                                    <div class="color-dot" style="background-color: <?= $color ?>"></div>
                                <?php endforeach; ?>
                            </div>
                            <div class="palette-info">
                                <div class="palette-title"><?= htmlspecialchars($palette['title'] ?: '无标题') ?></div>
                                <div class="palette-meta">分类: <?= htmlspecialchars($palette['category_name'] ?: '未分类') ?></div>
                            </div>
                            <div class="palette-stats">
                                <span>❤️ <?= $palette['likes_count'] ?></span>
                                <span>👁️ <?= $palette['views_count'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="section">
                <div class="section-title">
                    最新添加的调色板
                    <a href="palettes.php?action=add" class="btn btn-success" style="float: right;">添加新调色板</a>
                </div>
                <div class="palette-list">
                    <?php foreach ($recentPalettes as $palette): ?>
                        <?php $colors = json_decode($palette['colors'], true); ?>
                        <div class="palette-item">
                            <div class="palette-colors">
                                <?php foreach ($colors as $color): ?>
                                    <div class="color-dot" style="background-color: <?= $color ?>"></div>
                                <?php endforeach; ?>
                            </div>
                            <div class="palette-info">
                                <div class="palette-title"><?= htmlspecialchars($palette['title'] ?: '无标题') ?></div>
                                <div class="palette-meta">
                                    分类: <?= htmlspecialchars($palette['category_name'] ?: '未分类') ?> | 
                                    创建时间: <?= date('Y-m-d H:i', strtotime($palette['created_at'])) ?>
                                </div>
                            </div>
                            <div class="palette-stats">
                                <span>❤️ <?= $palette['likes_count'] ?></span>
                                <span>👁️ <?= $palette['views_count'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

        <?php endif; ?>
    </div>
</body>
</html>
