<?php
// Color Hunt 安装脚本
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Hunt - 安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .install-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
        }

        .install-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .install-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .install-subtitle {
            opacity: 0.9;
            font-size: 18px;
        }

        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-status {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .status-success {
            background: #10b981;
        }

        .status-error {
            background: #ef4444;
        }

        .status-warning {
            background: #f59e0b;
        }

        .step-content {
            opacity: 0.9;
            line-height: 1.6;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 24px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 600;
            margin: 5px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .btn-success {
            background: #10b981;
            border-color: #10b981;
        }

        .btn-success:hover {
            background: #059669;
            border-color: #059669;
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1 class="install-title">🎨 Color Hunt</h1>
            <p class="install-subtitle">安装向导</p>
        </div>

        <?php
        $checks = [];
        $allPassed = true;

        // 检查 PHP 版本
        $phpVersion = PHP_VERSION;
        $phpOk = version_compare($phpVersion, '7.4.0', '>=');
        $checks[] = [
            'title' => 'PHP 版本检查',
            'status' => $phpOk ? 'success' : 'error',
            'content' => $phpOk 
                ? "✅ PHP $phpVersion (满足要求 >= 7.4.0)"
                : "❌ PHP $phpVersion (需要 >= 7.4.0)"
        ];
        if (!$phpOk) $allPassed = false;

        // 检查必要的扩展
        $extensions = ['json', 'mbstring'];
        foreach ($extensions as $ext) {
            $loaded = extension_loaded($ext);
            $checks[] = [
                'title' => "PHP 扩展: $ext",
                'status' => $loaded ? 'success' : 'error',
                'content' => $loaded ? "✅ 已加载" : "❌ 未加载"
            ];
            if (!$loaded) $allPassed = false;
        }

        // 检查目录权限
        $directories = ['data', 'cache', 'logs'];
        foreach ($directories as $dir) {
            $exists = file_exists($dir);
            $writable = $exists ? is_writable($dir) : false;
            
            if (!$exists) {
                $created = @mkdir($dir, 0755, true);
                $exists = $created;
                $writable = $created;
            }
            
            $checks[] = [
                'title' => "目录: $dir",
                'status' => ($exists && $writable) ? 'success' : 'error',
                'content' => ($exists && $writable) 
                    ? "✅ 存在且可写" 
                    : "❌ " . (!$exists ? "不存在" : "不可写")
            ];
            if (!$exists || !$writable) $allPassed = false;
        }

        // 检查数据文件
        $dataFile = 'data/palettes.json';
        $dataExists = file_exists($dataFile);
        if (!$dataExists) {
            // 创建初始数据文件
            $initialData = [
                'palettes' => [
                    [
                        'id' => 1,
                        'colors' => ['#1e3a8a', '#3b82f6', '#93c5fd', '#dbeafe'],
                        'likes' => 11,
                        'time' => '2 hours',
                        'category' => 'pastel',
                        'created_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'id' => 2,
                        'colors' => ['#f3e8ff', '#c084fc', '#8b5cf6', '#374151'],
                        'likes' => 122,
                        'time' => 'Yesterday',
                        'category' => 'vintage',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                    ],
                    [
                        'id' => 3,
                        'colors' => ['#be185d', '#ec4899', '#f9a8d4', '#fce7f3'],
                        'likes' => 168,
                        'time' => '2 days',
                        'category' => 'warm',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
                    ],
                    [
                        'id' => 4,
                        'colors' => ['#065f46', '#059669', '#34d399', '#d1fae5'],
                        'likes' => 296,
                        'time' => '3 days',
                        'category' => 'neon',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                    ]
                ]
            ];
            
            $created = @file_put_contents($dataFile, json_encode($initialData, JSON_PRETTY_PRINT));
            $dataExists = $created !== false;
        }

        $checks[] = [
            'title' => '数据文件',
            'status' => $dataExists ? 'success' : 'error',
            'content' => $dataExists 
                ? "✅ data/palettes.json 存在" 
                : "❌ 无法创建数据文件"
        ];
        if (!$dataExists) $allPassed = false;

        // 检查配置文件
        $configExists = file_exists('config.php');
        $checks[] = [
            'title' => '配置文件',
            'status' => $configExists ? 'success' : 'warning',
            'content' => $configExists 
                ? "✅ config.php 存在" 
                : "⚠️ config.php 不存在（可选）"
        ];

        // 显示检查结果
        foreach ($checks as $check) {
            echo '<div class="step">';
            echo '<div class="step-title">';
            echo '<span class="step-status status-' . $check['status'] . '">';
            echo $check['status'] === 'success' ? '✓' : ($check['status'] === 'error' ? '✗' : '!');
            echo '</span>';
            echo $check['title'];
            echo '</div>';
            echo '<div class="step-content">' . $check['content'] . '</div>';
            echo '</div>';
        }
        ?>

        <?php if ($allPassed): ?>
            <div class="step">
                <div class="step-title">
                    <span class="step-status status-success">✓</span>
                    安装完成
                </div>
                <div class="step-content">
                    🎉 所有检查都通过了！您的 Color Hunt 网站已经准备就绪。
                </div>
            </div>
        <?php else: ?>
            <div class="step">
                <div class="step-title">
                    <span class="step-status status-error">✗</span>
                    安装失败
                </div>
                <div class="step-content">
                    ❌ 有一些问题需要解决。请检查上面的错误信息并修复后重新运行安装。
                    
                    <div class="code">
                        # 手动创建目录和设置权限：<br>
                        mkdir -p data cache logs<br>
                        chmod 755 data cache logs
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="actions">
            <?php if ($allPassed): ?>
                <a href="index.php" class="btn btn-success">🏠 访问主页</a>
                <a href="admin/index.php" class="btn">⚙️ 管理后台</a>
                <a href="demo.html" class="btn">📖 功能演示</a>
            <?php else: ?>
                <a href="install.php" class="btn">🔄 重新检查</a>
                <a href="test.php" class="btn">🧪 运行测试</a>
            <?php endif; ?>
        </div>

        <?php if ($allPassed): ?>
            <div class="step">
                <div class="step-title">
                    <span class="step-status status-success">ℹ️</span>
                    使用说明
                </div>
                <div class="step-content">
                    <strong>主要功能：</strong><br>
                    • 🎨 浏览和搜索颜色调色板<br>
                    • 📋 一键复制颜色代码<br>
                    • ❤️ 为喜欢的调色板点赞<br>
                    • 📱 完美的移动端体验<br>
                    • ⚙️ 后台管理调色板<br><br>
                    
                    <strong>快捷键：</strong><br>
                    • Ctrl+K (Cmd+K) - 聚焦搜索框<br>
                    • ESC - 清空搜索<br><br>
                    
                    <strong>API 接口：</strong><br>
                    • GET /api.php?action=palettes - 获取调色板列表<br>
                    • POST /api.php?action=like - 点赞调色板<br>
                    • POST /api.php?action=palette - 创建调色板
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
