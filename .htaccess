# Color Hunt - Apache 配置文件

# 启用重写引擎
RewriteEngine On

# 安全设置
# 隐藏 Apache 版本信息
ServerTokens Prod
ServerSignature Off

# 防止访问敏感文件
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 防止访问配置和数据文件
<FilesMatch "^(config|api)\.php$">
    <RequireAll>
        Require all granted
    </RequireAll>
</FilesMatch>

# 保护数据目录
<Directory "data">
    Options -Indexes
    <FilesMatch "\.json$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# 保护日志目录
<Directory "logs">
    Options -Indexes
    Order Allow,Deny
    Deny from all
</Directory>

# 保护缓存目录
<Directory "cache">
    Options -Indexes
    Order Allow,Deny
    Deny from all
</Directory>

# 启用压缩
<IfModule mod_deflate.c>
    # 压缩文本文件
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    
    # 排除已压缩的文件
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2|sit|rar)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</IfModule>

# 启用缓存
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片缓存 1 个月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS 和 JS 缓存 1 周
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType text/javascript "access plus 1 week"
    
    # HTML 缓存 1 小时
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON 缓存 10 分钟
    ExpiresByType application/json "access plus 10 minutes"
    
    # 字体缓存 1 个月
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
</IfModule>

# 设置缓存控制头
<IfModule mod_headers.c>
    # 移除 ETag
    Header unset ETag
    FileETag None
    
    # 设置缓存头
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
    
    # API 响应不缓存
    <FilesMatch "api\.php$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # CORS 头（如果需要跨域访问）
    # Header always set Access-Control-Allow-Origin "*"
    # Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    # Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# URL 重写规则
# 美化 URL（可选）
# RewriteRule ^palette/([0-9]+)/?$ index.php?id=$1 [L,QSA]
# RewriteRule ^category/([a-zA-Z0-9-]+)/?$ index.php?category=$1 [L,QSA]
# RewriteRule ^search/([^/]+)/?$ index.php?search=$1 [L,QSA]

# 强制 HTTPS（如果需要）
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 移除 www（如果需要）
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# 错误页面
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# PHP 设置
<IfModule mod_php7.c>
    # 隐藏 PHP 版本
    php_flag expose_php off
    
    # 设置时区
    php_value date.timezone "Asia/Shanghai"
    
    # 内存限制
    php_value memory_limit 128M
    
    # 上传限制
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    
    # 会话设置
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_only_cookies 1
    
    # 错误报告（生产环境关闭）
    php_flag display_errors off
    php_flag log_errors on
    php_value error_log logs/php_errors.log
</IfModule>

# 防止热链接（可选）
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif)$ - [NC,F,L]

# 限制请求方法
<LimitExcept GET POST HEAD>
    Order Allow,Deny
    Deny from all
</LimitExcept>

# 防止目录浏览
Options -Indexes

# 默认字符集
AddDefaultCharset UTF-8

# MIME 类型
AddType application/javascript .js
AddType text/css .css
AddType image/svg+xml .svg
AddType font/woff .woff
AddType font/woff2 .woff2
