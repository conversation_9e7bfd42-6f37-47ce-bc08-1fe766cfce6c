<?php
// 只在直接访问 API 时设置头部
if (basename($_SERVER['PHP_SELF']) === 'api.php') {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
    header('Access-Control-Allow-Headers: Content-Type');
}

// 简单的数据存储（实际项目中应该使用数据库）
$dataFile = 'data/palettes.json';

// 确保数据目录存在
if (!file_exists('data')) {
    mkdir('data', 0755, true);
}

// 初始化数据
if (!file_exists($dataFile)) {
    $initialData = [
        'palettes' => [
            [
                'id' => 1,
                'colors' => ['#1e3a8a', '#3b82f6', '#93c5fd', '#dbeafe'],
                'likes' => 11,
                'time' => '2 hours',
                'category' => 'pastel',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'colors' => ['#f3e8ff', '#c084fc', '#8b5cf6', '#374151'],
                'likes' => 122,
                'time' => 'Yesterday',
                'category' => 'vintage',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'id' => 3,
                'colors' => ['#be185d', '#ec4899', '#f9a8d4', '#fce7f3'],
                'likes' => 168,
                'time' => '2 days',
                'category' => 'warm',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'id' => 4,
                'colors' => ['#065f46', '#059669', '#34d399', '#d1fae5'],
                'likes' => 296,
                'time' => '3 days',
                'category' => 'neon',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
            ],
            [
                'id' => 5,
                'colors' => ['#166534', '#f97316', '#fbbf24', '#fef3c7'],
                'likes' => 206,
                'time' => '4 days',
                'category' => 'retro',
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 days'))
            ],
            [
                'id' => 6,
                'colors' => ['#0891b2', '#fef3c7', '#fde047', '#ca8a04'],
                'likes' => 294,
                'time' => '5 days',
                'category' => 'summer',
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
            ],
            [
                'id' => 7,
                'colors' => ['#374151', '#6b7280', '#9ca3af', '#d1d5db'],
                'likes' => 334,
                'time' => '6 days',
                'category' => 'dark',
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 days'))
            ],
            [
                'id' => 8,
                'colors' => ['#7c2d12', '#dc2626', '#84cc16', '#bef264'],
                'likes' => 257,
                'time' => '1 week',
                'category' => 'fall',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ]
        ]
    ];
    file_put_contents($dataFile, json_encode($initialData, JSON_PRETTY_PRINT));
}

// 读取数据
function getData() {
    global $dataFile;
    $data = file_get_contents($dataFile);
    return json_decode($data, true);
}

// 保存数据
function saveData($data) {
    global $dataFile;
    file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
}

// 获取请求方法和路径
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$path = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGet($path);
            break;
        case 'POST':
            handlePost($path);
            break;
        case 'PUT':
            handlePut($path);
            break;
        case 'DELETE':
            handleDelete($path);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($action) {
    $data = getData();
    
    switch ($action) {
        case 'palettes':
            $search = $_GET['search'] ?? '';
            $category = $_GET['category'] ?? '';
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 12);
            
            $palettes = $data['palettes'];
            
            // 筛选
            if ($search || $category) {
                $palettes = array_filter($palettes, function($palette) use ($search, $category) {
                    $matchesSearch = empty($search) || 
                        stripos(implode('', $palette['colors']), $search) !== false ||
                        stripos($palette['category'], $search) !== false;
                    $matchesCategory = empty($category) || 
                        strtolower($palette['category']) === strtolower($category);
                    return $matchesSearch && $matchesCategory;
                });
            }
            
            // 排序
            $sort = $_GET['sort'] ?? 'newest';
            switch ($sort) {
                case 'popular':
                    usort($palettes, function($a, $b) {
                        return $b['likes'] - $a['likes'];
                    });
                    break;
                case 'oldest':
                    usort($palettes, function($a, $b) {
                        return strtotime($a['created_at']) - strtotime($b['created_at']);
                    });
                    break;
                default: // newest
                    usort($palettes, function($a, $b) {
                        return strtotime($b['created_at']) - strtotime($a['created_at']);
                    });
            }
            
            // 分页
            $total = count($palettes);
            $offset = ($page - 1) * $limit;
            $palettes = array_slice($palettes, $offset, $limit);
            
            echo json_encode([
                'palettes' => array_values($palettes),
                'total' => $total,
                'page' => $page,
                'pages' => ceil($total / $limit)
            ]);
            break;
            
        case 'palette':
            $id = (int)($_GET['id'] ?? 0);
            $palette = array_filter($data['palettes'], function($p) use ($id) {
                return $p['id'] === $id;
            });
            
            if (empty($palette)) {
                http_response_code(404);
                echo json_encode(['error' => 'Palette not found']);
                return;
            }
            
            echo json_encode(array_values($palette)[0]);
            break;
            
        case 'categories':
            $categories = array_unique(array_column($data['palettes'], 'category'));
            sort($categories);
            echo json_encode($categories);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Action not found']);
    }
}

function handlePost($action) {
    $input = json_decode(file_get_contents('php://input'), true);
    $data = getData();
    
    switch ($action) {
        case 'like':
            $id = (int)($input['id'] ?? 0);
            $paletteIndex = array_search($id, array_column($data['palettes'], 'id'));
            
            if ($paletteIndex === false) {
                http_response_code(404);
                echo json_encode(['error' => 'Palette not found']);
                return;
            }
            
            $data['palettes'][$paletteIndex]['likes']++;
            saveData($data);
            
            echo json_encode([
                'success' => true,
                'likes' => $data['palettes'][$paletteIndex]['likes']
            ]);
            break;
            
        case 'palette':
            $colors = $input['colors'] ?? [];
            $category = $input['category'] ?? 'custom';
            
            if (count($colors) !== 4) {
                http_response_code(400);
                echo json_encode(['error' => 'Palette must have exactly 4 colors']);
                return;
            }
            
            $newId = max(array_column($data['palettes'], 'id')) + 1;
            $newPalette = [
                'id' => $newId,
                'colors' => $colors,
                'likes' => 0,
                'time' => 'Just now',
                'category' => $category,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $data['palettes'][] = $newPalette;
            saveData($data);
            
            echo json_encode($newPalette);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Action not found']);
    }
}

function handlePut($action) {
    // 更新操作
    echo json_encode(['message' => 'PUT method not implemented yet']);
}

function handleDelete($action) {
    // 删除操作
    echo json_encode(['message' => 'DELETE method not implemented yet']);
}

// 生成随机颜色调色板
function generateRandomPalette() {
    $colors = [];
    $baseHue = rand(0, 360);
    
    for ($i = 0; $i < 4; $i++) {
        $hue = ($baseHue + ($i * 30)) % 360;
        $saturation = rand(40, 90);
        $lightness = rand(30, 80);
        
        $colors[] = hslToHex($hue, $saturation, $lightness);
    }
    
    return $colors;
}

function hslToHex($h, $s, $l) {
    $h /= 360;
    $s /= 100;
    $l /= 100;
    
    $r = $g = $b = $l;
    
    if ($s !== 0) {
        $hue2rgb = function($p, $q, $t) {
            if ($t < 0) $t += 1;
            if ($t > 1) $t -= 1;
            if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
            if ($t < 1/2) return $q;
            if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
            return $p;
        };
        
        $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
        $p = 2 * $l - $q;
        $r = $hue2rgb($p, $q, $h + 1/3);
        $g = $hue2rgb($p, $q, $h);
        $b = $hue2rgb($p, $q, $h - 1/3);
    }
    
    $r = round($r * 255);
    $g = round($g * 255);
    $b = round($b * 255);
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>
