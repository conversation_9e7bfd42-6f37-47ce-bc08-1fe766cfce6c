<?php
// 引入配置文件
require_once '../config.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $dataFile = '../data/palettes.json';
        $data = json_decode(file_get_contents($dataFile), true);
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_category':
                    $oldCategory = sanitize_input($_POST['old_category']);
                    $newCategory = sanitize_input($_POST['new_category']);
                    
                    if (empty($newCategory)) {
                        throw new Exception("新分类名称不能为空");
                    }
                    
                    // 更新所有使用该分类的调色板
                    $updated = 0;
                    foreach ($data['palettes'] as &$palette) {
                        if ($palette['category'] === $oldCategory) {
                            $palette['category'] = $newCategory;
                            $updated++;
                        }
                    }
                    
                    file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
                    $success = "成功更新 {$updated} 个调色板的分类！";
                    break;
                    
                case 'merge_categories':
                    $sourceCategory = sanitize_input($_POST['source_category']);
                    $targetCategory = sanitize_input($_POST['target_category']);
                    
                    if ($sourceCategory === $targetCategory) {
                        throw new Exception("源分类和目标分类不能相同");
                    }
                    
                    // 将源分类的所有调色板合并到目标分类
                    $merged = 0;
                    foreach ($data['palettes'] as &$palette) {
                        if ($palette['category'] === $sourceCategory) {
                            $palette['category'] = $targetCategory;
                            $merged++;
                        }
                    }
                    
                    file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
                    $success = "成功将 {$merged} 个调色板从 '{$sourceCategory}' 合并到 '{$targetCategory}'！";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 读取分类数据
try {
    $dataFile = '../data/palettes.json';
    $data = json_decode(file_get_contents($dataFile), true);
    $palettes = $data['palettes'] ?? [];
    
    // 统计分类信息
    $categoryStats = [];
    foreach ($palettes as $palette) {
        $category = $palette['category'];
        if (!isset($categoryStats[$category])) {
            $categoryStats[$category] = [
                'name' => $category,
                'count' => 0,
                'total_likes' => 0,
                'latest_created' => $palette['created_at'] ?? ''
            ];
        }
        $categoryStats[$category]['count']++;
        $categoryStats[$category]['total_likes'] += $palette['likes'];
        
        if (isset($palette['created_at']) && $palette['created_at'] > $categoryStats[$category]['latest_created']) {
            $categoryStats[$category]['latest_created'] = $palette['created_at'];
        }
    }
    
    // 按调色板数量排序
    uasort($categoryStats, function($a, $b) {
        return $b['count'] - $a['count'];
    });
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $categoryStats = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - Color Hunt</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .admin-header {
            background: #1e293b;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            font-size: 24px;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: background 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #3b82f6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            color: #1e293b;
        }

        .btn {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 14px;
        }

        .btn:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        .category-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .category-table th,
        .category-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .category-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .category-name {
            font-weight: 600;
            color: #1e293b;
            text-transform: capitalize;
        }

        .category-badge {
            background: #e0e7ff;
            color: #3730a3;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .success {
            background: #f0fdf4;
            color: #166534;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            margin-bottom: 20px;
        }

        .error {
            background: #fef2f2;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .admin-nav {
                flex-direction: column;
                gap: 15px;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .category-table {
                font-size: 14px;
            }

            .category-table th,
            .category-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-logo">🎨 Color Hunt 管理后台</div>
            <div class="nav-links">
                <a href="index.php">仪表板</a>
                <a href="palettes.php">调色板管理</a>
                <a href="categories.php" class="active">分类管理</a>
                <a href="../index.php" target="_blank">查看前台</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">分类管理</h1>
        </div>

        <?php if (isset($success)): ?>
            <div class="success">✅ <?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error">❌ <?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= count($categoryStats) ?></div>
                <div class="stat-label">总分类数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= array_sum(array_column($categoryStats, 'count')) ?></div>
                <div class="stat-label">总调色板数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= array_sum(array_column($categoryStats, 'total_likes')) ?></div>
                <div class="stat-label">总点赞数</div>
            </div>
        </div>

        <!-- 分类列表 -->
        <div class="card">
            <h2 class="card-title">分类列表</h2>
            <table class="category-table">
                <thead>
                    <tr>
                        <th>分类名称</th>
                        <th>调色板数量</th>
                        <th>总点赞数</th>
                        <th>最新更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categoryStats as $category): ?>
                        <tr>
                            <td>
                                <span class="category-name"><?= htmlspecialchars($category['name']) ?></span>
                            </td>
                            <td>
                                <span class="category-badge"><?= $category['count'] ?> 个</span>
                            </td>
                            <td>❤️ <?= $category['total_likes'] ?></td>
                            <td><?= $category['latest_created'] ? date('Y-m-d H:i', strtotime($category['latest_created'])) : '未知' ?></td>
                            <td>
                                <button class="btn btn-small" onclick="editCategory('<?= htmlspecialchars($category['name']) ?>')">重命名</button>
                                <button class="btn btn-warning btn-small" onclick="mergeCategory('<?= htmlspecialchars($category['name']) ?>')">合并</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 分类操作 -->
        <div class="card">
            <h2 class="card-title">分类操作</h2>
            
            <form method="POST" id="categoryForm" style="display: none;">
                <input type="hidden" name="action" id="formAction">
                <input type="hidden" name="old_category" id="oldCategory">
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">源分类</label>
                        <select name="source_category" id="sourceCategory" class="form-select">
                            <?php foreach ($categoryStats as $category): ?>
                                <option value="<?= htmlspecialchars($category['name']) ?>"><?= htmlspecialchars(ucfirst($category['name'])) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" id="targetLabel">新分类名称</label>
                        <input type="text" name="new_category" id="newCategory" class="form-input" placeholder="输入新的分类名称">
                        <select name="target_category" id="targetCategory" class="form-select" style="display: none;">
                            <?php foreach ($categoryStats as $category): ?>
                                <option value="<?= htmlspecialchars($category['name']) ?>"><?= htmlspecialchars(ucfirst($category['name'])) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="cancelOperation()">取消</button>
                    <button type="submit" class="btn btn-success" id="submitBtn">确认</button>
                </div>
            </form>
            
            <div id="operationHint" style="text-align: center; color: #64748b; padding: 40px;">
                选择一个分类进行重命名或合并操作
            </div>
        </div>
    </div>

    <script>
        function editCategory(categoryName) {
            document.getElementById('formAction').value = 'update_category';
            document.getElementById('oldCategory').value = categoryName;
            document.getElementById('sourceCategory').value = categoryName;
            document.getElementById('sourceCategory').disabled = true;
            document.getElementById('newCategory').style.display = 'block';
            document.getElementById('targetCategory').style.display = 'none';
            document.getElementById('targetLabel').textContent = '新分类名称';
            document.getElementById('submitBtn').textContent = '重命名';
            document.getElementById('categoryForm').style.display = 'block';
            document.getElementById('operationHint').style.display = 'none';
        }

        function mergeCategory(categoryName) {
            document.getElementById('formAction').value = 'merge_categories';
            document.getElementById('sourceCategory').value = categoryName;
            document.getElementById('sourceCategory').disabled = true;
            document.getElementById('newCategory').style.display = 'none';
            document.getElementById('targetCategory').style.display = 'block';
            document.getElementById('targetLabel').textContent = '目标分类';
            document.getElementById('submitBtn').textContent = '合并';
            document.getElementById('categoryForm').style.display = 'block';
            document.getElementById('operationHint').style.display = 'none';
        }

        function cancelOperation() {
            document.getElementById('categoryForm').style.display = 'none';
            document.getElementById('operationHint').style.display = 'block';
            document.getElementById('sourceCategory').disabled = false;
            document.getElementById('newCategory').value = '';
        }
    </script>
</body>
</html>
