<?php
// 配置文件

// 数据库配置（如果需要使用数据库）
define('DB_HOST', 'localhost');
define('DB_NAME', 'color_hunt');
define('DB_USER', 'root');
define('DB_PASS', '');

// 网站配置
define('SITE_NAME', 'Color Hunt');
define('SITE_URL', 'http://localhost');
define('SITE_DESCRIPTION', '发现美丽的颜色调色板');

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // 1小时

// 分页配置
define('PALETTES_PER_PAGE', 12);
define('MAX_PALETTES_PER_PAGE', 50);

// 文件上传配置
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// API配置
define('API_RATE_LIMIT', 100); // 每小时请求限制
define('API_CACHE_DURATION', 300); // 5分钟

// 安全配置
define('CSRF_TOKEN_NAME', '_token');
define('SESSION_LIFETIME', 7200); // 2小时

// 颜色生成配置
define('DEFAULT_PALETTE_SIZE', 4);
define('MIN_PALETTE_SIZE', 2);
define('MAX_PALETTE_SIZE', 8);

// 错误报告
if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 启用会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 工具函数
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validate_hex_color($color) {
    return preg_match('/^#[a-fA-F0-9]{6}$/', $color);
}

function generate_csrf_token() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verify_csrf_token($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && 
           hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function get_client_ip() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// 简单的缓存类
class SimpleCache {
    private $cache_dir;
    
    public function __construct($cache_dir = 'cache') {
        $this->cache_dir = $cache_dir;
        if (!file_exists($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    public function get($key) {
        $file = $this->cache_dir . '/' . md5($key) . '.cache';
        if (!file_exists($file)) {
            return null;
        }
        
        $data = unserialize(file_get_contents($file));
        if ($data['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $data['value'];
    }
    
    public function set($key, $value, $duration = null) {
        if ($duration === null) {
            $duration = CACHE_DURATION;
        }
        
        $file = $this->cache_dir . '/' . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expires' => time() + $duration
        ];
        
        file_put_contents($file, serialize($data));
    }
    
    public function delete($key) {
        $file = $this->cache_dir . '/' . md5($key) . '.cache';
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    public function clear() {
        $files = glob($this->cache_dir . '/*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
}

// 初始化缓存
$cache = new SimpleCache();

// 响应头设置
function set_json_header() {
    header('Content-Type: application/json; charset=utf-8');
}

function set_cache_headers($duration = null) {
    if ($duration === null) {
        $duration = CACHE_DURATION;
    }
    
    header('Cache-Control: public, max-age=' . $duration);
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $duration) . ' GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
}

function set_no_cache_headers() {
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// 错误处理
function handle_error($message, $code = 500) {
    http_response_code($code);
    if (isset($_SERVER['HTTP_ACCEPT']) && 
        strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        set_json_header();
        echo json_encode(['error' => $message, 'code' => $code]);
    } else {
        echo "<h1>错误 $code</h1><p>$message</p>";
    }
    exit;
}

// 日志记录
function log_message($message, $level = 'INFO') {
    $log_file = 'logs/' . date('Y-m-d') . '.log';
    if (!file_exists('logs')) {
        mkdir('logs', 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = get_client_ip();
    $log_entry = "[$timestamp] [$level] [$ip] $message" . PHP_EOL;
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
