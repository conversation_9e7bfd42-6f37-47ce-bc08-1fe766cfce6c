<?php
echo "<h1>调试信息</h1>";

echo "<h2>1. 数据文件检查</h2>";
$dataFile = 'data/palettes.json';
if (file_exists($dataFile)) {
    echo "✅ 数据文件存在<br>";
    $content = file_get_contents($dataFile);
    echo "文件大小: " . strlen($content) . " 字节<br>";
    
    $data = json_decode($content, true);
    if ($data) {
        echo "✅ JSON 解析成功<br>";
        echo "调色板数量: " . count($data['palettes']) . "<br>";
    } else {
        echo "❌ JSON 解析失败: " . json_last_error_msg() . "<br>";
    }
} else {
    echo "❌ 数据文件不存在<br>";
}

echo "<h2>2. fetchPalettes 函数测试</h2>";
// 引入 index.php 中的函数
function fetchPalettes($search = '', $category = '', $page = 1, $limit = 12) {
    $dataFile = 'data/palettes.json';
    
    // 确保数据文件存在
    if (!file_exists($dataFile)) {
        return ['palettes' => [], 'total' => 0, 'page' => 1, 'pages' => 0];
    }
    
    $data = json_decode(file_get_contents($dataFile), true);
    if (!$data || !isset($data['palettes'])) {
        return ['palettes' => [], 'total' => 0, 'page' => 1, 'pages' => 0];
    }
    
    $palettes = $data['palettes'];
    
    // 筛选
    if ($search || $category) {
        $palettes = array_filter($palettes, function($palette) use ($search, $category) {
            $matchesSearch = empty($search) || 
                stripos(implode('', $palette['colors']), $search) !== false ||
                stripos($palette['category'], $search) !== false;
            $matchesCategory = empty($category) || 
                strtolower($palette['category']) === strtolower($category);
            return $matchesSearch && $matchesCategory;
        });
    }
    
    // 排序（按最新）
    usort($palettes, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    // 分页
    $total = count($palettes);
    $offset = ($page - 1) * $limit;
    $palettes = array_slice($palettes, $offset, $limit);
    
    return [
        'palettes' => array_values($palettes),
        'total' => $total,
        'page' => $page,
        'pages' => ceil($total / $limit)
    ];
}

$result = fetchPalettes();
echo "返回的调色板数量: " . count($result['palettes']) . "<br>";
echo "总数: " . $result['total'] . "<br>";
echo "页数: " . $result['pages'] . "<br>";

echo "<h2>3. 调色板数据预览</h2>";
if (!empty($result['palettes'])) {
    foreach ($result['palettes'] as $index => $palette) {
        echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
        echo "<strong>调色板 #" . $palette['id'] . "</strong><br>";
        echo "分类: " . $palette['category'] . "<br>";
        echo "点赞: " . $palette['likes'] . "<br>";
        echo "颜色: ";
        foreach ($palette['colors'] as $color) {
            echo "<span style='background: $color; color: white; padding: 2px 5px; margin: 2px;'>$color</span> ";
        }
        echo "<br>";
        echo "</div>";
        
        if ($index >= 2) break; // 只显示前3个
    }
} else {
    echo "❌ 没有调色板数据";
}

echo "<h2>4. PHP 错误检查</h2>";
if (function_exists('error_get_last')) {
    $error = error_get_last();
    if ($error) {
        echo "最后的错误: " . print_r($error, true);
    } else {
        echo "✅ 没有PHP错误";
    }
}

echo "<h2>5. 服务器信息</h2>";
echo "PHP 版本: " . PHP_VERSION . "<br>";
echo "当前目录: " . getcwd() . "<br>";
echo "脚本路径: " . __FILE__ . "<br>";
?>
