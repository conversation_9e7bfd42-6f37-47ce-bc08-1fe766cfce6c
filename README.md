# Color Hunt - 颜色调色板网站

一个类似 Color Hunt 的颜色调色板网站，使用 PHP 开发，具有响应式设计和优化的性能。

## 功能特性

### 🎨 核心功能
- **颜色调色板展示** - 美观的网格布局展示调色板
- **颜色复制** - 点击颜色块快速复制颜色代码
- **调色板复制** - 一键复制整个调色板的所有颜色
- **搜索功能** - 支持按颜色代码和分类搜索
- **分类筛选** - 多种预设分类（复古、霓虹、暖色等）
- **点赞系统** - 为喜欢的调色板点赞

### 📱 响应式设计
- **移动端优化** - 完美适配手机和平板设备
- **PC端体验** - 大屏幕下的最佳布局
- **触摸友好** - 移动设备上的良好交互体验

### ⚡ 性能优化
- **输出压缩** - 启用 Gzip 压缩减少传输大小
- **缓存机制** - 智能缓存提高加载速度
- **懒加载** - 卡片动画和渐进式加载
- **防抖搜索** - 减少不必要的搜索请求
- **分页系统** - 避免一次加载过多内容

### 🛠 技术特性
- **纯 PHP 开发** - 无需复杂框架
- **原生 CSS** - 不依赖第三方 CSS 框架
- **现代 JavaScript** - ES6+ 语法和现代 API
- **RESTful API** - 标准的 API 接口设计
- **文件存储** - 简单的 JSON 文件数据存储

## 安装和使用

### 环境要求
- PHP 7.4 或更高版本
- Web 服务器（Apache/Nginx）
- 支持文件写入权限

### 快速开始

1. **下载项目文件**
   ```bash
   # 将所有文件放到你的 Web 服务器目录
   ```

2. **设置权限**
   ```bash
   chmod 755 data/
   chmod 755 cache/
   chmod 755 logs/
   ```

3. **访问网站**
   ```
   http://your-domain.com/
   ```

### 文件结构
```
color-hunt/
├── index.php          # 主页面
├── api.php           # API 接口
├── config.php        # 配置文件
├── README.md         # 说明文档
├── data/             # 数据存储目录
│   └── palettes.json # 调色板数据
├── cache/            # 缓存目录
└── logs/             # 日志目录
```

## 使用说明

### 基本操作

1. **浏览调色板**
   - 在主页面浏览各种颜色调色板
   - 使用侧边栏分类筛选

2. **搜索调色板**
   - 在顶部搜索框输入关键词
   - 支持颜色代码和分类名称搜索
   - 使用 Ctrl+K (Mac: Cmd+K) 快速聚焦搜索框

3. **复制颜色**
   - 点击任意颜色块复制单个颜色代码
   - 点击调色板卡片复制整个调色板

4. **点赞功能**
   - 点击心形图标为调色板点赞
   - 点赞数会实时更新

### 键盘快捷键
- `Ctrl/Cmd + K` - 聚焦搜索框
- `ESC` - 清空搜索（在搜索框聚焦时）

## API 接口

### 获取调色板列表
```
GET /api.php?action=palettes
```

参数：
- `search` - 搜索关键词
- `category` - 分类筛选
- `page` - 页码（默认：1）
- `limit` - 每页数量（默认：12）
- `sort` - 排序方式（newest/popular/oldest）

### 获取单个调色板
```
GET /api.php?action=palette&id={id}
```

### 点赞调色板
```
POST /api.php?action=like
Content-Type: application/json

{
  "id": 1
}
```

### 创建调色板
```
POST /api.php?action=palette
Content-Type: application/json

{
  "colors": ["#ff0000", "#00ff00", "#0000ff", "#ffff00"],
  "category": "custom"
}
```

### 获取分类列表
```
GET /api.php?action=categories
```

## 自定义配置

### 修改配置
编辑 `config.php` 文件来自定义设置：

```php
// 分页配置
define('PALETTES_PER_PAGE', 12);

// 缓存配置
define('CACHE_DURATION', 3600);

// 网站信息
define('SITE_NAME', 'Your Color Hunt');
```

### 添加新分类
在 `index.php` 中修改 `$categories` 数组：

```php
$categories = ['New', 'Popular', 'Your Category', ...];
```

### 自定义样式
所有样式都在 `index.php` 的 `<style>` 标签中，可以直接修改：

```css
/* 修改主色调 */
.logo-icon {
    background: linear-gradient(45deg, #your-color, #your-color2);
}
```

## 性能优化建议

### 服务器配置
1. **启用 Gzip 压缩**
   ```apache
   # .htaccess
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript application/json
   </IfModule>
   ```

2. **设置缓存头**
   ```apache
   # .htaccess
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 month"
       ExpiresByType application/javascript "access plus 1 month"
   </IfModule>
   ```

3. **启用 PHP OPcache**
   ```ini
   ; php.ini
   opcache.enable=1
   opcache.memory_consumption=128
   opcache.max_accelerated_files=4000
   ```

### 数据库升级
对于大量数据，建议升级到数据库存储：

```sql
CREATE TABLE palettes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    colors JSON NOT NULL,
    likes INT DEFAULT 0,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod 755 data/ cache/ logs/
   chown www-data:www-data data/ cache/ logs/
   ```

2. **API 不工作**
   - 检查 PHP 版本是否支持
   - 确认 JSON 扩展已启用
   - 查看错误日志

3. **样式不显示**
   - 检查 CSS 是否正确加载
   - 确认没有 JavaScript 错误

### 调试模式
在 `config.php` 中启用调试：

```php
define('ENVIRONMENT', 'development');
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 许可证

MIT License - 可自由使用和修改。

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的调色板展示功能
- 响应式设计
- 搜索和筛选功能
- 点赞系统
- API 接口
