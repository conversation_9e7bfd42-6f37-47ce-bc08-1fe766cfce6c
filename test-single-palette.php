<?php
// 模拟只有一个调色板的情况来测试布局
$singlePalette = [
    'id' => 1,
    'colors' => ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
    'likes' => 42,
    'time' => '2小时前',
    'category' => 'test'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单个调色板布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .main-content {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }

        .sidebar {
            width: 200px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 400px; /* 模拟较高的侧边栏 */
        }

        .category-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .category-item {
            padding: 12px 16px;
            border-radius: 8px;
            text-decoration: none;
            color: #64748b;
            transition: all 0.3s;
        }

        .category-item.active {
            background: #f1f5f9;
            color: #1e293b;
        }

        /* 修复后的 Palette Grid */
        .palette-grid {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 320px));
            gap: 25px;
            align-content: start;
            justify-content: start;
            max-width: 100%;
        }

        .palette-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            height: fit-content;
            max-width: 320px;
            width: 100%;
            justify-self: start;
        }

        .palette-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .color-strip {
            height: 200px;
            display: flex;
        }

        .color-block {
            flex: 1;
            position: relative;
            transition: transform 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .color-code {
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .color-block:hover .color-code {
            opacity: 1;
        }

        .palette-info {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fafafa;
        }

        .like-btn {
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            font-size: 14px;
            transition: color 0.3s;
        }

        .like-btn:hover {
            color: #e11d48;
        }

        .time-info {
            color: #94a3b8;
            font-size: 12px;
        }

        .test-info {
            background: #e0f2fe;
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c4a6e;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .palette-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 300px));
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .palette-grid {
                grid-template-columns: 1fr;
                justify-content: center;
            }
            
            .palette-card {
                max-width: 100%;
                justify-self: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <strong>🧪 布局测试</strong> - 这个页面模拟只有一个调色板的情况，用来测试调色板卡片是否会异常拉伸。
            <br>✅ 修复后：调色板卡片应该保持合理的宽度，不会拉伸到与侧边栏一样高。
        </div>
        
        <div class="main-content">
            <aside class="sidebar">
                <nav class="category-list">
                    <a href="#" class="category-item">All</a>
                    <a href="#" class="category-item active">Test</a>
                    <a href="#" class="category-item">Popular</a>
                    <a href="#" class="category-item">New</a>
                    <a href="#" class="category-item">Vintage</a>
                    <a href="#" class="category-item">Pastel</a>
                    <a href="#" class="category-item">Dark</a>
                    <a href="#" class="category-item">Light</a>
                </nav>
            </aside>

            <main class="palette-grid">
                <div class="palette-card" data-id="<?= $singlePalette['id'] ?>">
                    <div class="color-strip">
                        <?php foreach ($singlePalette['colors'] as $color): ?>
                            <div class="color-block" style="background-color: <?= $color ?>" data-color="<?= $color ?>">
                                <span class="color-code"><?= strtoupper($color) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="palette-info">
                        <button class="like-btn">
                            ❤️ <span class="like-count"><?= $singlePalette['likes'] ?></span>
                        </button>
                        <span class="time-info"><?= $singlePalette['time'] ?></span>
                    </div>
                </div>
            </main>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.php" style="color: #3b82f6; text-decoration: none;">← 返回主页</a>
        </div>
    </div>
</body>
</html>
