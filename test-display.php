<?php
// 简单的测试页面来验证调色板显示
$dataFile = 'data/palettes.json';
$data = json_decode(file_get_contents($dataFile), true);
$palettes = $data['palettes'] ?? [];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调色板显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .palette {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .colors {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        .color {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
        .info {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>调色板显示测试</h1>
    <p>数据文件中共有 <?= count($palettes) ?> 个调色板</p>
    
    <?php foreach ($palettes as $palette): ?>
        <div class="palette">
            <h3>调色板 #<?= $palette['id'] ?></h3>
            <div class="colors">
                <?php foreach ($palette['colors'] as $color): ?>
                    <div class="color" style="background-color: <?= $color ?>">
                        <?= $color ?>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="info">
                分类: <?= $palette['category'] ?> | 
                点赞: <?= $palette['likes'] ?> | 
                时间: <?= $palette['time'] ?>
            </div>
        </div>
    <?php endforeach; ?>
    
    <hr>
    <p><a href="index.php">返回主页</a> | <a href="admin/index.php">管理后台</a></p>
</body>
</html>
