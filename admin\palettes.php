<?php
// 引入配置文件
require_once '../config.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $dataFile = '../data/palettes.json';
        $data = json_decode(file_get_contents($dataFile), true);
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $colors = [
                        $_POST['color1'],
                        $_POST['color2'],
                        $_POST['color3'],
                        $_POST['color4']
                    ];
                    
                    // 验证颜色格式
                    foreach ($colors as $color) {
                        if (!validate_hex_color($color)) {
                            throw new Exception("无效的颜色格式: $color");
                        }
                    }
                    
                    $newId = max(array_column($data['palettes'], 'id')) + 1;
                    $newPalette = [
                        'id' => $newId,
                        'colors' => $colors,
                        'likes' => 0,
                        'time' => 'Just now',
                        'category' => sanitize_input($_POST['category']),
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $data['palettes'][] = $newPalette;
                    file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
                    $success = "调色板添加成功！";
                    break;
                    
                case 'delete':
                    $id = (int)$_POST['id'];
                    $data['palettes'] = array_filter($data['palettes'], function($p) use ($id) {
                        return $p['id'] !== $id;
                    });
                    $data['palettes'] = array_values($data['palettes']);
                    file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
                    $success = "调色板删除成功！";
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 读取调色板数据
try {
    $dataFile = '../data/palettes.json';
    $data = json_decode(file_get_contents($dataFile), true);
    $palettes = $data['palettes'] ?? [];
    
    // 分页
    $page = (int)($_GET['page'] ?? 1);
    $perPage = 10;
    $total = count($palettes);
    $totalPages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    $palettes = array_slice($palettes, $offset, $perPage);
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $palettes = [];
}

$categories = ['pastel', 'vintage', 'retro', 'neon', 'gold', 'light', 'dark', 'warm', 'cold', 'summer', 'fall', 'winter', 'spring'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调色板管理 - Color Hunt</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .admin-header {
            background: #1e293b;
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            font-size: 24px;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: background 0.3s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #3b82f6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: bold;
            color: #1e293b;
        }

        .btn {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 14px;
        }

        .btn:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .form-select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 16px;
            background: white;
        }

        .color-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .color-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            border: 2px solid #e5e7eb;
        }

        .palette-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .palette-table th,
        .palette-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .palette-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .palette-colors {
            display: flex;
            gap: 4px;
        }

        .color-dot {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .page-btn {
            padding: 8px 12px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.3s;
        }

        .page-btn:hover,
        .page-btn.active {
            border-color: #3b82f6;
            color: #3b82f6;
        }

        .success {
            background: #f0fdf4;
            color: #166534;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            margin-bottom: 20px;
        }

        .error {
            background: #fef2f2;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            margin-bottom: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
        }

        @media (max-width: 768px) {
            .admin-nav {
                flex-direction: column;
                gap: 15px;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .color-inputs {
                grid-template-columns: 1fr;
            }

            .palette-table {
                font-size: 14px;
            }

            .palette-table th,
            .palette-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-logo">🎨 Color Hunt 管理后台</div>
            <div class="nav-links">
                <a href="index.php">仪表板</a>
                <a href="palettes.php" class="active">调色板管理</a>
                <a href="../index.php" target="_blank">查看前台</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">调色板管理</h1>
            <button class="btn btn-success" onclick="showAddModal()">+ 添加调色板</button>
        </div>

        <?php if (isset($success)): ?>
            <div class="success">✅ <?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error">❌ <?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <table class="palette-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>颜色</th>
                    <th>分类</th>
                    <th>点赞数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($palettes as $palette): ?>
                    <tr>
                        <td>#<?= $palette['id'] ?></td>
                        <td>
                            <div class="palette-colors">
                                <?php foreach ($palette['colors'] as $color): ?>
                                    <div class="color-dot" style="background-color: <?= $color ?>" title="<?= $color ?>"></div>
                                <?php endforeach; ?>
                            </div>
                        </td>
                        <td><?= htmlspecialchars(ucfirst($palette['category'])) ?></td>
                        <td>❤️ <?= $palette['likes'] ?></td>
                        <td><?= isset($palette['created_at']) ? date('Y-m-d H:i', strtotime($palette['created_at'])) : $palette['time'] ?></td>
                        <td>
                            <button class="btn btn-danger btn-small" onclick="deletePalette(<?= $palette['id'] ?>)">删除</button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=<?= $i ?>" class="page-btn <?= $i === $page ? 'active' : '' ?>"><?= $i ?></a>
                <?php endfor; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- 添加调色板模态框 -->
    <div class="modal" id="addModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加新调色板</h3>
                <button class="close-btn" onclick="hideAddModal()">&times;</button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                
                <div class="form-group">
                    <label class="form-label">分类</label>
                    <select name="category" class="form-select" required>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= $cat ?>"><?= ucfirst($cat) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">颜色 (4个)</label>
                    <div class="color-inputs">
                        <?php for ($i = 1; $i <= 4; $i++): ?>
                            <div class="color-input-group">
                                <input type="color" id="colorPicker<?= $i ?>" onchange="updateColorInput(<?= $i ?>)">
                                <input type="text" name="color<?= $i ?>" id="colorInput<?= $i ?>" class="form-input" 
                                       placeholder="#000000" pattern="^#[0-9A-Fa-f]{6}$" required>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="hideAddModal()" style="margin-right: 10px;">取消</button>
                    <button type="submit" class="btn btn-success">添加</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('addModal').classList.add('show');
        }

        function hideAddModal() {
            document.getElementById('addModal').classList.remove('show');
        }

        function updateColorInput(index) {
            const picker = document.getElementById('colorPicker' + index);
            const input = document.getElementById('colorInput' + index);
            input.value = picker.value;
        }

        function deletePalette(id) {
            if (confirm('确定要删除这个调色板吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 初始化颜色选择器
        for (let i = 1; i <= 4; i++) {
            const input = document.getElementById('colorInput' + i);
            const picker = document.getElementById('colorPicker' + i);
            
            input.addEventListener('input', function() {
                if (this.value.match(/^#[0-9A-Fa-f]{6}$/)) {
                    picker.value = this.value;
                }
            });
            
            // 设置默认值
            const defaultColors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24'];
            input.value = defaultColors[i-1];
            picker.value = defaultColors[i-1];
        }

        // 点击模态框外部关闭
        document.getElementById('addModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAddModal();
            }
        });
    </script>
</body>
</html>
