<?php
// 简单的测试脚本
echo "Color Hunt 网站测试\n";
echo "==================\n\n";

// 测试 PHP 版本
echo "PHP 版本: " . PHP_VERSION . "\n";

// 测试必要的扩展
$required_extensions = ['json', 'mbstring'];
foreach ($required_extensions as $ext) {
    echo "扩展 $ext: " . (extension_loaded($ext) ? "✓ 已加载" : "✗ 未加载") . "\n";
}

// 测试目录权限
$directories = ['data', 'cache', 'logs'];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    echo "目录 $dir: " . (is_writable($dir) ? "✓ 可写" : "✗ 不可写") . "\n";
}

// 测试 API
echo "\n测试 API 接口:\n";

// 模拟 API 调用
$_GET['action'] = 'palettes';
ob_start();
try {
    include 'api.php';
    $response = ob_get_clean();
    $data = json_decode($response, true);
    
    if ($data && isset($data['palettes'])) {
        echo "✓ API 正常工作，返回 " . count($data['palettes']) . " 个调色板\n";
    } else {
        echo "✗ API 返回数据格式错误\n";
        echo "响应: " . substr($response, 0, 200) . "...\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "✗ API 错误: " . $e->getMessage() . "\n";
}

// 测试配置文件
echo "\n测试配置:\n";
try {
    include 'config.php';
    echo "✓ 配置文件加载成功\n";
    echo "网站名称: " . SITE_NAME . "\n";
    echo "缓存启用: " . (CACHE_ENABLED ? "是" : "否") . "\n";
} catch (Exception $e) {
    echo "✗ 配置文件错误: " . $e->getMessage() . "\n";
}

// 测试数据文件
echo "\n测试数据文件:\n";
$dataFile = 'data/palettes.json';
if (file_exists($dataFile)) {
    $data = json_decode(file_get_contents($dataFile), true);
    if ($data && isset($data['palettes'])) {
        echo "✓ 数据文件正常，包含 " . count($data['palettes']) . " 个调色板\n";
    } else {
        echo "✗ 数据文件格式错误\n";
    }
} else {
    echo "✗ 数据文件不存在\n";
}

echo "\n测试完成！\n";
echo "如果所有项目都显示 ✓，说明网站配置正确。\n";
echo "访问 http://localhost:8000 查看网站。\n";
?>
